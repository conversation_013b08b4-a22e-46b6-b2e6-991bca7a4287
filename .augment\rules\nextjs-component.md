---
trigger: always_on
---

# Component Architecture
- Unless a component explicitly requires client-side interactivity (e.g., event handlers like onClick, onChange, or hooks like useState, useEffect, useContext for client-side contexts), it **MUST** be a Server Component. Do not add `'use client'` at the top of a file unless absolutely necessary.
- When client-side interactivity is needed, isolate it to the smallest possible component (Leaf Components). Import these granular client components into Server Components that manage layout or data fetching. Do **NOT** make large layout sections or data-fetching components Client Components if only a small part needs interactivity.