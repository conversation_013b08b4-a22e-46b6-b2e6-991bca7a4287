'use client'

import React from 'react'
import Link from 'next/link'
import {
  ActionIcon,
  AppShell,
  Avatar,
  Box,
  Group,
  Image,
  Menu,
  NavLink,
  ScrollArea,
  Title,
  UnstyledButton,
} from '@mantine/core'
import { useDisclosure } from '@mantine/hooks'
import {
  AltArrowRight,
  Bell,
  CloseCircle,
  Documents,
  HamburgerMenu,
  Logout2,
  Settings,
  User,
  Widget,
} from '@solar-icons/react'

const user = {
  name: 'System',
  avatar: '/system.svg',
}

const navigationData = [
  {
    label: 'Dashboard',
    icon: Widget,
    href: '/',
  },
  {
    label: 'Market News',
    icon: Documents,
    children: [
      { label: 'Overview', href: '/market-news/overview' },
      { label: 'Forecasts', href: '/market-news/forecasts' },
      { label: 'Outlook', href: '/market-news/outlook' },
      { label: 'Real time', href: '/market-news/real-time' },
    ],
  },
]

export function Dashboard() {
  const [opened, { toggle }] = useDisclosure()

  return (
    <AppShell
      header={{ height: '4rem' }}
      navbar={{
        width: '14rem',
        breakpoint: 'sm',
        collapsed: { mobile: !opened },
      }}
      padding="sm"
    >
      <AppShell.Header>
        <Group h="100%" px="md" justify="space-between">
          <Group>
            <ActionIcon
              onClick={toggle}
              hiddenFrom="sm"
              variant="transparent"
              color="dimmed"
              aria-expanded={opened}
            >
              {opened ? (
                <CloseCircle size="2em" weight="LineDuotone" />
              ) : (
                <HamburgerMenu size="2em" weight="LineDuotone" />
              )}
            </ActionIcon>
            <UnstyledButton component={Link} href="/">
              <Group gap="sm">
                <Image src="/logo.svg" alt="Logo" w={32} h={32} />
                <Title size="h4">Mantine</Title>
              </Group>
            </UnstyledButton>
          </Group>
          <Menu shadow="md" position="bottom-end">
            <Menu.Target>
              <Avatar src={user.avatar} radius="xl">
                {user.name.slice(0, 2).toUpperCase()}
              </Avatar>
            </Menu.Target>
            <Menu.Dropdown>
              <Menu.Label>Account</Menu.Label>
              <Menu.Item leftSection={<User size={20} weight="BoldDuotone" />}>Profile</Menu.Item>
              <Menu.Item leftSection={<Settings size={20} weight="BoldDuotone" />}>
                Settings
              </Menu.Item>
              <Menu.Item leftSection={<Bell size={20} weight="BoldDuotone" />}>
                Notifications
              </Menu.Item>
              <Menu.Divider />
              <Menu.Item leftSection={<Logout2 size={20} weight="BoldDuotone" />} color="red">
                Sign out
              </Menu.Item>
            </Menu.Dropdown>
          </Menu>
        </Group>
      </AppShell.Header>

      <AppShell.Navbar>
        <ScrollArea>
          {navigationData.map((item) => {
            const hasChildren = item.children && item.children.length > 0
            if (hasChildren) {
              return (
                <NavLink
                  key={item.label}
                  label={item.label}
                  leftSection={<item.icon size={20} weight="BoldDuotone" />}
                  rightSection={
                    <AltArrowRight size={18} weight="BoldDuotone" className="mantine-rotate-rtl" />
                  }
                  childrenOffset="xl"
                >
                  {item.children?.map((child) => (
                    <NavLink
                      key={child.label}
                      label={child.label}
                      href={child.href}
                      component={Link}
                    />
                  ))}
                </NavLink>
              )
            }

            if (!item.href) {
              return null
            }

            return (
              <NavLink
                key={item.label}
                label={item.label}
                href={item.href}
                component={Link}
                leftSection={<item.icon size={20} weight="BoldDuotone" />}
              />
            )
          })}
        </ScrollArea>
      </AppShell.Navbar>

      <AppShell.Main>
        <Box>{/* Main content area */}</Box>
      </AppShell.Main>
    </AppShell>
  )
}
