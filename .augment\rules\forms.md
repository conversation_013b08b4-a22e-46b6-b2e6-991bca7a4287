---
trigger: model_decision
description: When working on forms
---

# Forms
- **ALL** form validations **MUST** be defined using Zod schemas. This includes client-side validation with Tanstack Form and server-side validation within Server Actions.
- When using Tanstack Form, the `onSubmit` handler should typically invoke a Server Action. Use the `useForm` hook's `handleSubmit` method.
- Inside Server Actions that receive form data, **ALWAYS** re-validate the data using the corresponding Zod schema before processing it or sending it to Convex. Return detailed error messages if validation fails. Do **NOT** trust client-side validation alone.
- Ensure that validation errors returned from Server Actions (derived from Zod) are correctly mapped and displayed by Tanstack Form components (e.g., using the `Field` component's error reporting).
- When using Server Actions with forms, ensure forms are functional even if JavaScript is disabled or fails to load on the client. This is a key benefit of Server Actions. Design forms so they can be submitted traditionally. Tanstack Form should enhance this, not break it.