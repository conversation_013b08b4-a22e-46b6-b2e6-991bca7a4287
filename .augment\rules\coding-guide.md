---
trigger: always_on
---

# General Coding Principles
- Add comments to explain why something is done a certain way if it's not immediately obvious from the code itself (e.g., complex logic, workarounds, business reasons). Do **NOT** comment on what the code is doing if it's self-explanatory (e.g., // increment i). Comments should add value.
- Functions **MUST** strive to do one thing and do it well. If a function is becoming too long (e.g., more than 20-30 lines, context-dependent) or is handling multiple distinct tasks, it **MUST** be broken down into smaller, more focused functions.
- If a piece of code (logic, configuration, constants) is used in multiple places, it **MUST** be extracted into a reusable function, component, constant, or configuration variable. Avoid copy-pasting code.
- Literal numbers or strings that have a specific meaning (other than very obvious ones like 0, 1, or empty string in certain contexts) **MUST** be defined as named constants (e.g., const MAX_LOGIN_ATTEMPTS = 5;).
- Ensure all generated code adheres to the project's established formatting rules (e.g., <PERSON><PERSON><PERSON>, <PERSON>SL<PERSON>). If no explicit formatter is mentioned, aim for standard TypeScript/JavaScript formatting conventions (indentation, spacing, line breaks).
- Where practical, prefer immutable data structures and operations. Avoid directly mutating objects or arrays received as props or from state. Instead, create new instances with the updated values (e.g., using spread syntax for objects/arrays, or immutable update patterns in Zustand).
- In functions, use guard clauses (early `return` statements for invalid conditions or edge cases) at the beginning of the function to reduce nesting and improve readability of the main logic path.
- For values that might change between environments (development, staging, production) or that are likely to be adjusted (e.g., API endpoints, feature flags, thresholds), these **MUST** be managed via environment variables or configuration files, not hardcoded.
- Prioritize writing clear, understandable, and correct code. Only apply performance optimizations if there is a demonstrated performance bottleneck, and ensure the optimization does not significantly harm readability or maintainability. Do **NOT** engage in premature optimization.
- **ALWAYS** use `async/await` for promise-based asynchronous operations to improve readability over chained `.then()/.catch()` where appropriate. Ensure all promises are properly handled (either awaited, returned, or explicitly handled with `.catch()` if not awaited) to avoid unhandled promise rejections.
- Prefer the simplest solution that effectively solves the problem. Avoid unnecessary complexity, overly clever code, or using advanced language features just for the sake of it if a simpler approach suffices.
- When interacting with external APIs or any data source whose structure is not guaranteed by your TypeScript types at runtime (even if types are provided for development), **ALWAYS** validate the received data (Zod is perfect for this at the boundary) before processing it. Do not implicitly trust external data.
- Organize code into logical modules (files or directories) based on feature or functionality. Aim for high cohesion within modules (related code stays together) and low coupling between modules (modules are as independent as possible).
- When accessing multiple properties from an object or props in a component/function, **PREFER** using destructuring for conciseness and clarity.