---
trigger: model_decision
description: When working on toasts with react-hot-toast
---

# Notifications
- The <Toaster /> component from `react-hot-toast` MUST be placed in the root layout file (app/layout.tsx or app/layout.jsx). This ensures it is available on all pages. It should be rendered within the <body> tag, typically outside the main children rendering but within any global client-side providers.
- To trigger a toast from a Client Component (any component with `'use client'`), import the toast function directly from react-hot-toast. Call toast.success('Message'), toast.error('Message'), etc., within event handlers or  appropriate client-side logic.
- When a Server Action is invoked via Tanstack Form's `handleSubmit` function (from the `useForm` hook), utilize the `onSuccess` and `onError` (or onSubmitSuccess/onSubmitError depending on the Tanstack Form version/API) callbacks provided by `useForm` or its submission handling. The Server Action **MUST** return a serializable result (e.g., { success: true, message: '...' } or { error: '...', details: '...' }). Inside these callbacks, inspect the result and call the appropriate toast function.
- For client-side operations that involve a promise (e.g., an API call made from a Client Component, not a Server Action), use `toast.promise()` to automatically handle loading, success, and error states of the toast.
- Avoid ad-hoc styling for every toast call unless specifically required for a unique case.