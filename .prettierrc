{"printWidth": 100, "singleQuote": true, "semi": false, "trailingComma": "es5", "plugins": ["@ianvs/prettier-plugin-sort-imports"], "importOrder": [".*styles.css$", "", "dayjs", "^react$", "^next$", "^next/.*$", "<BUILTIN_MODULES>", "^@mantine/(.*)$", "^@mantinex/(.*)$", "^@mantine-tests/(.*)$", "^@docs/(.*)$", "<THIRD_PARTY_MODULES>", "", "^@/.*$", "^../(?!.*.css$).*$", "^./(?!.*.css$).*$", "\\.css$"], "overrides": [{"files": "*.mdx", "options": {"printWidth": 70}}]}