'use client'

import Image from 'next/image'
import Link from 'next/link'
import { Button, Container, Group, Stack, Text, Title } from '@mantine/core'
import { Home, Refresh } from '@solar-icons/react'

export default function Error({ reset }: { error: Error & { digest: string }; reset: () => void }) {
  return (
    <Container size="sm" h="100vh" display="flex">
      <Stack gap="xl" align="center" justify="center">
        <Image src="/system.svg" alt="500 icon" width={240} height={240} priority />
        <Title fz="h1">Something bad just happened...</Title>
        <Text c="dimmed" ta="center" size="lg">
          Our servers could not handle your request. Don't worry, our development team was already
          notified. Try refreshing the page.
        </Text>
        <Group justify="center">
          <Button
            size="md"
            onClick={reset}
            leftSection={<Refresh weight="BoldDuotone" size={18} />}
            variant="gradient"
          >
            Try again
          </Button>
          <Button
            component={Link}
            size="md"
            href="/"
            leftSection={<Home weight="BoldDuotone" size={18} />}
            variant="transparent"
          >
            Go to Homepage
          </Button>
        </Group>
      </Stack>
    </Container>
  )
}
