---
trigger: always_on
---

# State Management
- To avoid prop drilling, use *Zustand* **ONLY**, instead of React Context, for client-side state that needs to be shared across multiple components and is not easily passed down through props, or for state that needs to persist across route changes on the client. Do **NOT** use Zustand for state that can be managed by React's `useState` within a single client component or for server-derived state that should be refetched.
- When creating Zustand stores, ensure they are defined outside of component render functions to prevent re-creation on every render. If a store needs initial props from a Server Component, pass them during the component's hydration phase on the client, typically within a `useEffect` hook in the top-level client component that uses the store.
- Do **NOT** attempt to directly access or modify Zustand stores within Server Components. If a Server Component needs to influence initial client state, pass data as props to a Client Component which then hydrates the Zustand store.