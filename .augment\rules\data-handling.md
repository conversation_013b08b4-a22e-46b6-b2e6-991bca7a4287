---
trigger: always_on
---

# Data Fetching and Mutations
- Directly await Convex queries within Server Components for data fetching. Do **NOT** use `useEffect` for initial data fetching in Server Components.
- All data mutations (create, update, delete) triggered by user interactions (e.g., form submissions, button clicks) **MUST** be implemented using Next.js Server Actions that call Convex functions. Define Server Actions in .ts files with the `'use server'` directive at the top, or inline within Server Components.
- Fetch data from Convex within Server Components by directly calling your Convex query functions. Ensure the Convex client is correctly initialized for server-side execution if necessary (follow Convex documentation for Next.js integration).
- When a Server Action is used, especially with Tanstack Form or any client-side logic that needs to react, it **MUST** return a serializable object. This object should clearly indicate success or failure and include user-friendly messages.
- Server Actions **MUST** include robust `try/catch` blocks. Errors from Convex or other operations should be caught and returned in a structured way (e.g., { error: 'message' }) so the client-side (e.g., Tanstack Form) can display them appropriately using toasts. Do **NOT** let raw errors propagate uncaught.
- After a successful mutation via a Server Action, **ALWAYS** use `revalidatePath` or `revalidateTag` to ensure stale data is refreshed on the client. Choose the most specific path or tag possible.
- For every route segment that performs data fetching or might have loading states, **ALWAYS** implement a `loading.tsx` file using a React Suspense boundary. Similarly, for routes that can encounter errors during rendering or data fetching, **ALWAYS** implement an `error.tsx` file to handle them gracefully.
- For API-like endpoints that are not directly tied to form submissions or UI mutations (e.g., webhooks, specific GET requests for data not fitting RSC patterns), use Route Handlers. For form submissions and UI-triggered mutations, **PRIORITIZE** Server Actions.