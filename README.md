# Next.js Template

A modern, full-stack web application template built with Next.js, Material UI, Convex, and TypeScript.

## Project Overview

This template provides a solid foundation for building full-stack web applications with a modern tech stack. It includes:

- Server-side rendering with Next.js App Router
- Type-safe backend with Convex
- Material UI for component library
- Authentication ready with Convex Auth
- Dark/light mode support
- Form handling with TanStack React Form
- State management with Zustand
- TypeScript for type safety

## Technologies and Frameworks

### Frontend

- [Next.js 15](https://nextjs.org/) - React framework with App Router
- [React 19](https://react.dev/) - UI library
- [Material UI 7](https://mui.com/) - Component library
- [TanStack React Form](https://tanstack.com/form) - Form state management
- [TanStack React Table](https://tanstack.com/table) - Table UI components
- [Zustand](https://zustand-demo.pmnd.rs/) - State management
- [TypeScript](https://www.typescriptlang.org/) - Type safety

### Backend

- [Convex](https://www.convex.dev/) - Backend-as-a-service with real-time data sync
- [Zod](https://zod.dev/) - Schema validation

## Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/nextjs-template.git
cd nextjs-template
```

2. Install dependencies:

```bash
pnpm install
```

3. Set up environment variables:
   - Create a `.env.local` file in the root directory
   - Add the following variables:

```
NEXT_PUBLIC_CONVEX_URL=your_convex_deployment_url
CONVEX_SITE_URL=your_site_url
```

## Development Workflow

### Running the app locally

1. Start the Convex development server:

```bash
pnpm dlx convex dev
```

2. In a separate terminal, start the Next.js development server:

```bash
pnpm dev
```

3. Open [http://localhost:3000](http://localhost:3000) in your browser to see the application.

### Development commands

- `pnpm dev` - Start the development server
- `pnpm build` - Build the application for production
- `pnpm start` - Start the production server
- `pnpm lint` - Run ESLint to check code quality

## Project Structure

```
nextjs-template/
├── convex/                 # Convex backend code
│   ├── auth.config.ts      # Authentication configuration
│   ├── auth.ts             # Authentication logic
│   ├── schema.ts           # Database schema
│   └── _generated/         # Generated Convex files (do not edit)
├── public/                 # Static assets
├── src/
│   ├── app/                # Next.js App Router pages
│   ├── components/         # Reusable UI components
│   ├── providers/          # React context providers
│   ├── stores/             # Zustand state stores
│   └── styles/             # Theme and global styles
├── .env.local              # Environment variables (create this)
├── next.config.ts          # Next.js configuration
├── package.json            # Project dependencies and scripts
└── tsconfig.json           # TypeScript configuration
```

## Deployment

### Deploying to Vercel

1. Push your code to a GitHub repository
2. Connect your repository to [Vercel](https://vercel.com)
3. Configure environment variables in the Vercel dashboard
4. Deploy your application

### Deploying Convex

1. Install the Convex CLI if you haven't already:

```bash
pnpm dlx convex
```

2. Deploy your Convex functions:

```bash
pnpm dlx convex deploy
```

3. Update your environment variables with the production Convex URL

## Contributing

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/your-feature-name`
3. Commit your changes: `git commit -m 'Add some feature'`
4. Push to the branch: `git push origin feature/your-feature-name`
5. Open a pull request

### Coding Standards

- Follow the ESLint configuration
- Write clean, maintainable code
- Include appropriate tests for new features
- Update documentation as needed

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Learn More

- [Next.js Documentation](https://nextjs.org/docs)
- [Material UI Documentation](https://mui.com/material-ui/getting-started/)
- [Convex Documentation](https://docs.convex.dev/)
- [TanStack React Form Documentation](https://tanstack.com/form/latest/docs/overview)
- [Zustand Documentation](https://github.com/pmndrs/zustand)
