---
trigger: model_decision
description: When working on tables
---

# Data Tables
- Data for Tanstack Table should ideally be fetched in a Server Component and then passed as a prop to the Client Component that renders the table. For client-side sorting, pagination, and filtering that doesn't require refetching, Tanstack Table's built-in utilities are fine.
- If implementing server-side pagination, sorting, or filtering with Tanstack Table, ensure that the state changes in the table trigger new data fetches (e.g., via Server Actions or by updating URL query params that cause a Server Component to refetch) that pass the new parameters to your Convex queries.
- When passing `data` and `columns` props to Tanstack Table, ensure they are memoized (e.g., using `useMemo`) if they are generated or transformed within the rendering Client Component, to prevent unnecessary re-renders of the table.