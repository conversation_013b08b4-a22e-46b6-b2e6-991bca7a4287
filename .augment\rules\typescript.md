---
trigger: always_on
---

# TypeScript
- The any type **MUST NOT** be used. If a type is unknown, use `unknown` and perform type checking or assertion before use. For genuinely dynamic structures where any might seem tempting, try to define a more specific type, use generics, or leverage Record<string, unknown> or Record<string, any_more_specific_type_if_possible>.
- Allow TypeScript to infer types for variables and function return values where the inference is simple and obvious. However, for exported functions, complex function return types, or object properties that are part of a public API or complex data structure, **ALWAYS** provide explicit type annotations.
- Actively use TypeScript's built-in utility types (Partial<T>, Readonly<T>, Record<K,T>, Pick<T,K>, Omit<T,K>, ReturnType<T>, Parameters<T>, etc.) to construct new types from existing ones. Do **NOT** manually redefine types that could be easily derived.
- For identifiers (e.g., UserId, ProductId) or other primitive values that have specific domain meaning, prefer creating branded types or distinct string/number literal unions over using plain `string` or `number`.
- **PREFER** using `type` aliases over `interface` for defining object shapes, function types, and union/intersection types. Use `interface` primarily when declaration merging is explicitly needed.
- Mark properties as `readonly` on types/interfaces if they should not be reassigned after object creation. For arrays that should not be mutated, use `ReadonlyArray<T>` or `readonly T[]`.
- Do **NOT** use the non-null assertion operator (!) unless you are absolutely certain (and can justify with a comment) that a value will not be `null` or `undefined` at that point, and TypeScript's type checker cannot infer it. Prefer explicit `null` checks, type guards, or optional chaining (?.).
- When creating functions or React components that operate on or return data of various types while maintaining type safety, **ALWAYS** use generics.
- For simple, fixed sets of string values (e.g., statuses, modes), **PREFER** string literal unions over `enum`. Use `enum` when you need numeric values, reverse mapping, or when the set of values might be extended more formally.
- Since Zod is used for validation, **ALWAYS** derive TypeScript types from Zod schemas using `z.infer<typeof yourSchema>` for data that is validated by that schema. Do **NOT** manually define a separate TypeScript type that mirrors a Zod schema.
- When defining custom error types or structured error objects (e.g., for Server Action returns), ensure they have a consistent shape. Consider a base error type if multiple custom errors are used.
- Keep related types, interfaces, and Zod schemas close to the modules or components that use them. For widely shared types, consider a dedicated `types` or `schemas` directory (e.g., lib/types, lib/schemas).