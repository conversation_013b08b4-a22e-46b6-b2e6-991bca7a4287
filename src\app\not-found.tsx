'use client'

import Image from 'next/image'
import Link from 'next/link'
import { <PERSON><PERSON>, Container, Stack, Text, Title } from '@mantine/core'
import { Home } from '@solar-icons/react'

export default function NotFound() {
  return (
    <Container size="sm" h="100vh" display="flex">
      <Stack gap="xl" align="center" justify="center">
        <Image src="/lost.svg" alt="404 icon" width={240} height={240} priority />
        <Title fz="h1">Nothing to see here</Title>
        <Text c="dimmed" ta="center" size="lg">
          Page you are trying to open does not exist. You may have mistyped the address, or the page
          has been moved to another URL. If you think this is an error, contact support.
        </Text>
        <Button
          component={Link}
          size="md"
          href="/"
          leftSection={<Home weight="BoldDuotone" size={18} />}
          variant="gradient"
        >
          Go to Homepage
        </Button>
      </Stack>
    </Container>
  )
}
