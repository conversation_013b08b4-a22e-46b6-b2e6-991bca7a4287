---
trigger: always_on
---

# Next.js App Router (Routing)
- Do not place route-specific data or state directly in a layout if it needs to change per page. Use pages or templates for that.
- When generating pages or components that fetch data, suggest or automatically include `loading.tsx` at the segment level or wrap data-dependent components in `<Suspense>`. Ensure Suspense boundaries are placed effectively to avoid blocking the entire page.
- If a data fetch for a specific resource returns null/undefined, call `notFound()` to render the 404 UI. Ensure a `not-found.tsx` file exists.
- When organizing sections of an application (e.g., `(marketing)`, `(dashboard)`), use route groups.
- When designing complex dashboards or multi-view interfaces, consider Parallel Routes. Ensure `default.tsx` files are created for each slot.
- For modal dialogs that should have their own URL but display over existing content, suggest Intercepting Routes.